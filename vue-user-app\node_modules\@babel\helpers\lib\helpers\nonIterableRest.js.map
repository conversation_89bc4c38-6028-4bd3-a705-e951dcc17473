{"version": 3, "names": ["_nonIterableRest", "TypeError"], "sources": ["../../src/helpers/nonIterableRest.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _nonIterableRest() {\n  throw new TypeError(\n    \"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\",\n  );\n}\n"], "mappings": ";;;;;;AAEe,SAASA,gBAAgBA,CAAA,EAAG;EACzC,MAAM,IAAIC,SAAS,CACjB,2IACF,CAAC;AACH", "ignoreList": []}