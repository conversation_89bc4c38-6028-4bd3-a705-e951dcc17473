<template>
  <div class="session-stats">
    <div class="stats-header">
      <h3>
        <span class="stats-icon">📊</span>
        会话统计
      </h3>
      <div class="today-sessions">
        今日: {{ todaySessions }}次
      </div>
    </div>
    
    <div class="stats-content">
      <!-- 今日统计 -->
      <div class="today-stats">
        <div class="stat-card primary">
          <div class="stat-icon">🔥</div>
          <div class="stat-info">
            <div class="stat-number">{{ todaySessions }}</div>
            <div class="stat-label">今日会话</div>
          </div>
        </div>
        
        <div class="stat-card secondary">
          <div class="stat-icon">📈</div>
          <div class="stat-info">
            <div class="stat-number">{{ totalSessions }}</div>
            <div class="stat-label">总会话数</div>
          </div>
        </div>
      </div>
      
      <!-- 最近7天趋势 -->
      <div class="week-trend">
        <h4>最近7天趋势</h4>
        <div class="trend-chart">
          <div 
            v-for="day in weekSessions" 
            :key="day.date"
            class="chart-bar"
          >
            <div 
              class="bar"
              :style="{ height: getBarHeight(day.count) + '%' }"
              :title="`${formatDate(day.date)}: ${day.count}次会话`"
            ></div>
            <div class="bar-label">{{ getDayLabel(day.date) }}</div>
            <div class="bar-count">{{ day.count }}</div>
          </div>
        </div>
      </div>
      
      <!-- 会话时间分布 -->
      <div class="time-distribution">
        <h4>活跃时段</h4>
        <div class="time-slots">
          <div 
            v-for="slot in timeSlots" 
            :key="slot.period"
            class="time-slot"
            :class="slot.level"
          >
            <div class="slot-period">{{ slot.period }}</div>
            <div class="slot-indicator">
              <div class="indicator-bar" :style="{ width: slot.percentage + '%' }"></div>
            </div>
            <div class="slot-count">{{ slot.count }}次</div>
          </div>
        </div>
      </div>
      
      <!-- 统计摘要 -->
      <div class="stats-summary">
        <div class="summary-item">
          <span class="summary-label">平均每日:</span>
          <span class="summary-value">{{ averageDaily }}次</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">最高单日:</span>
          <span class="summary-value">{{ maxDaily }}次</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">活跃天数:</span>
          <span class="summary-value">{{ activeDays }}天</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { userStore } from '../stores/user.js'

export default {
  name: 'SessionStats',
  setup() {
    const todaySessions = computed(() => userStore.getTodaySessions())
    const weekSessions = computed(() => userStore.getWeekSessions())
    const totalSessions = computed(() => {
      return userStore.sessions.reduce((total, session) => total + session.count, 0)
    })
    
    const averageDaily = computed(() => {
      if (userStore.sessions.length === 0) return 0
      return Math.round(totalSessions.value / userStore.sessions.length)
    })
    
    const maxDaily = computed(() => {
      if (userStore.sessions.length === 0) return 0
      return Math.max(...userStore.sessions.map(s => s.count))
    })
    
    const activeDays = computed(() => userStore.sessions.length)
    
    // 模拟时间段分布数据
    const timeSlots = computed(() => {
      const slots = [
        { period: '早晨 6-9', count: Math.floor(Math.random() * 5) + 1 },
        { period: '上午 9-12', count: Math.floor(Math.random() * 8) + 3 },
        { period: '下午 12-15', count: Math.floor(Math.random() * 6) + 2 },
        { period: '下午 15-18', count: Math.floor(Math.random() * 10) + 5 },
        { period: '晚上 18-21', count: Math.floor(Math.random() * 8) + 4 },
        { period: '夜晚 21-24', count: Math.floor(Math.random() * 4) + 1 }
      ]
      
      const maxCount = Math.max(...slots.map(s => s.count))
      
      return slots.map(slot => ({
        ...slot,
        percentage: maxCount > 0 ? (slot.count / maxCount) * 100 : 0,
        level: slot.count >= maxCount * 0.7 ? 'high' : 
               slot.count >= maxCount * 0.4 ? 'medium' : 'low'
      }))
    })

    const getBarHeight = (count) => {
      const maxCount = Math.max(...weekSessions.value.map(s => s.count))
      return maxCount > 0 ? (count / maxCount) * 100 : 0
    }

    const formatDate = (dateStr) => {
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    }

    const getDayLabel = (dateStr) => {
      const date = new Date(dateStr)
      const today = new Date()
      const diffTime = today.getTime() - date.getTime()
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) return '今天'
      if (diffDays === 1) return '昨天'
      if (diffDays === 2) return '前天'
      
      const days = ['日', '一', '二', '三', '四', '五', '六']
      return '周' + days[date.getDay()]
    }

    return {
      todaySessions,
      weekSessions,
      totalSessions,
      averageDaily,
      maxDaily,
      activeDays,
      timeSlots,
      getBarHeight,
      formatDate,
      getDayLabel
    }
  }
}
</script>

<style scoped>
.session-stats {
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  color: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 24px rgba(108, 92, 231, 0.3);
  position: relative;
  overflow: hidden;
}

.session-stats::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stats-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.stats-icon {
  font-size: 1.2rem;
}

.today-sessions {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.today-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-card.primary {
  background: rgba(255, 255, 255, 0.2);
}

.stat-card.secondary {
  background: rgba(255, 255, 255, 0.1);
}

.stat-icon {
  font-size: 1.5rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
}

.week-trend h4,
.time-distribution h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  opacity: 0.9;
}

.trend-chart {
  display: flex;
  align-items: end;
  gap: 0.5rem;
  height: 80px;
  margin-bottom: 1.5rem;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px 4px 0 0;
  width: 100%;
  min-height: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.bar:hover {
  background: rgba(255, 255, 255, 0.5);
}

.bar-label {
  font-size: 0.7rem;
  margin-top: 0.3rem;
  opacity: 0.8;
}

.bar-count {
  font-size: 0.6rem;
  margin-top: 0.2rem;
  opacity: 0.6;
}

.time-slots {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-bottom: 1.5rem;
}

.time-slot {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.slot-period {
  font-size: 0.8rem;
  min-width: 80px;
  opacity: 0.9;
}

.slot-indicator {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.indicator-bar {
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.time-slot.high .indicator-bar {
  background: rgba(255, 255, 255, 0.8);
}

.time-slot.medium .indicator-bar {
  background: rgba(255, 255, 255, 0.6);
}

.time-slot.low .indicator-bar {
  background: rgba(255, 255, 255, 0.4);
}

.slot-count {
  font-size: 0.8rem;
  min-width: 30px;
  text-align: right;
  opacity: 0.8;
}

.stats-summary {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.summary-label {
  opacity: 0.8;
}

.summary-value {
  font-weight: 600;
}

@media (max-width: 768px) {
  .today-stats {
    grid-template-columns: 1fr;
  }
  
  .stats-header {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .time-slot {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
  
  .slot-indicator {
    width: 100%;
  }
}
</style>
