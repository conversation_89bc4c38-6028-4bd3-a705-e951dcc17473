<template>
  <div class="home">
    <header class="header">
      <div class="header-content">
        <div class="header-left">
          <div class="user-avatar">
            <img :src="userAvatar" :alt="userStore.user?.username" />
          </div>
          <div class="user-greeting">
            <h1>欢迎回来，{{ userStore.user?.username }}！</h1>
            <p>{{ getCurrentGreeting() }}</p>
          </div>
        </div>
        <div class="header-right">
          <button @click="handleLogout" class="logout-btn">
            <span class="logout-icon">🚪</span>
            退出登录
          </button>
        </div>
      </div>
    </header>

    <main class="main-content">
      <!-- 顶部统计卡片 -->
      <div class="stats-overview">
        <div class="overview-card sessions">
          <div class="card-icon">📊</div>
          <div class="card-content">
            <div class="card-number">{{ todaySessions }}</div>
            <div class="card-label">今日会话</div>
          </div>
        </div>

        <div class="overview-card todos">
          <div class="card-icon">📋</div>
          <div class="card-content">
            <div class="card-number">{{ todoCount }}</div>
            <div class="card-label">待办任务</div>
          </div>
        </div>

        <div class="overview-card completed">
          <div class="card-icon">✅</div>
          <div class="card-content">
            <div class="card-number">{{ completedCount }}</div>
            <div class="card-label">已办任务</div>
          </div>
        </div>

        <div class="overview-card checkin">
          <div class="card-icon">📅</div>
          <div class="card-content">
            <div class="card-number">{{ consecutiveDays }}</div>
            <div class="card-label">连续打卡</div>
          </div>
        </div>
      </div>

      <!-- 主要功能区域 -->
      <div class="dashboard-grid">
        <!-- 左侧列 -->
        <div class="dashboard-left">
          <!-- 会话统计 -->
          <SessionStats />

          <!-- 任务管理 -->
          <TaskManager />
        </div>

        <!-- 右侧列 -->
        <div class="dashboard-right">
          <!-- 天气预报 -->
          <WeatherWidget />

          <!-- 打卡功能 -->
          <CheckInWidget />

          <!-- 用户信息卡片 -->
          <div class="user-info-card">
            <h3>
              <span class="info-icon">👤</span>
              个人信息
            </h3>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">用户名:</span>
                <span class="info-value">{{ userStore.user?.username }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">邮箱:</span>
                <span class="info-value">{{ userStore.user?.email }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">注册时间:</span>
                <span class="info-value">{{ formatDate(userStore.user?.createdAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { userStore } from '../stores/user.js'
import SessionStats from '../components/SessionStats.vue'
import TaskManager from '../components/TaskManager.vue'
import WeatherWidget from '../components/WeatherWidget.vue'
import CheckInWidget from '../components/CheckInWidget.vue'

export default {
  name: 'Home',
  components: {
    SessionStats,
    TaskManager,
    WeatherWidget,
    CheckInWidget
  },
  setup() {
    const router = useRouter()

    // 计算属性
    const todaySessions = computed(() => userStore.getTodaySessions())
    const todoCount = computed(() => userStore.todoTasks.length)
    const completedCount = computed(() => userStore.completedTasks.length)
    const consecutiveDays = computed(() => userStore.getConsecutiveCheckInDays())

    // 生成用户头像
    const userAvatar = computed(() => {
      const username = userStore.user?.username || 'User'
      const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe']
      const colorIndex = username.charCodeAt(0) % colors.length
      const initial = username.charAt(0).toUpperCase()

      // 创建SVG头像
      return `data:image/svg+xml,${encodeURIComponent(`
        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="20" r="20" fill="${colors[colorIndex]}"/>
          <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-weight="bold">${initial}</text>
        </svg>
      `)}`
    })

    const handleLogout = () => {
      userStore.logout()
      router.push('/login')
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const getCurrentGreeting = () => {
      const hour = new Date().getHours()
      if (hour < 6) return '夜深了，注意休息 🌙'
      if (hour < 9) return '早上好！新的一天开始了 🌅'
      if (hour < 12) return '上午好！工作顺利 ☀️'
      if (hour < 14) return '中午好！记得吃午饭 🍽️'
      if (hour < 18) return '下午好！继续加油 💪'
      if (hour < 22) return '晚上好！辛苦了一天 🌆'
      return '夜深了，早点休息 🌙'
    }

    return {
      userStore,
      todaySessions,
      todoCount,
      completedCount,
      consecutiveDays,
      userAvatar,
      handleLogout,
      formatDate,
      getCurrentGreeting
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-greeting h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-greeting p {
  margin: 0.2rem 0 0 0;
  font-size: 1rem;
  opacity: 0.9;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.logout-icon {
  font-size: 1rem;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.overview-card {
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.overview-card.sessions {
  border-left-color: #6c5ce7;
}

.overview-card.todos {
  border-left-color: #fdcb6e;
}

.overview-card.completed {
  border-left-color: #00b894;
}

.overview-card.checkin {
  border-left-color: #e17055;
}

.card-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.card-number {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.2rem;
}

.card-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: start;
}

.dashboard-left,
.dashboard-right {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.user-info-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-info-card h3 {
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  font-size: 1.1rem;
}

.info-icon {
  font-size: 1.2rem;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #666;
  font-size: 0.9rem;
}

.info-value {
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-left {
    flex-direction: column;
    gap: 0.8rem;
  }

  .user-greeting h1 {
    font-size: 1.4rem;
  }

  .user-greeting p {
    font-size: 0.9rem;
  }

  .main-content {
    padding: 1.5rem 1rem;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-grid {
    gap: 1.5rem;
  }

  .overview-card {
    padding: 1rem;
  }

  .card-number {
    font-size: 1.5rem;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 0.5rem;
  }

  .main-content {
    padding: 1rem 0.5rem;
  }

  .overview-card {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .card-icon {
    font-size: 1.5rem;
  }
}
</style>
