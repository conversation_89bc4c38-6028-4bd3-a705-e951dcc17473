<template>
  <div class="home">
    <header class="header">
      <div class="header-content">
        <h1>欢迎来到主页</h1>
        <div class="user-info">
          <span>欢迎，{{ userStore.user?.username }}！</span>
          <button @click="handleLogout" class="logout-btn">退出登录</button>
        </div>
      </div>
    </header>
    
    <main class="main-content">
      <div class="welcome-section">
        <h2>用户信息</h2>
        <div class="user-card">
          <div class="user-detail">
            <strong>用户名:</strong> {{ userStore.user?.username }}
          </div>
          <div class="user-detail">
            <strong>邮箱:</strong> {{ userStore.user?.email }}
          </div>
          <div class="user-detail">
            <strong>注册时间:</strong> {{ formatDate(userStore.user?.createdAt) }}
          </div>
        </div>
      </div>
      
      <div class="features-section">
        <h2>功能特性</h2>
        <div class="features-grid">
          <div class="feature-card">
            <h3>🔐 安全登录</h3>
            <p>提供安全可靠的用户登录功能</p>
          </div>
          <div class="feature-card">
            <h3>📝 用户注册</h3>
            <p>简单快捷的用户注册流程</p>
          </div>
          <div class="feature-card">
            <h3>🏠 个人主页</h3>
            <p>个性化的用户主页体验</p>
          </div>
          <div class="feature-card">
            <h3>💾 数据持久化</h3>
            <p>本地存储用户数据和登录状态</p>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { userStore } from '../stores/user.js'

export default {
  name: 'Home',
  setup() {
    const router = useRouter()

    const handleLogout = () => {
      userStore.logout()
      router.push('/login')
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    return {
      userStore,
      handleLogout,
      formatDate
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.user-info span {
  font-size: 1.1rem;
  font-weight: 500;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.welcome-section {
  margin-bottom: 4rem;
}

.welcome-section h2 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.user-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.user-detail {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.user-detail:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.user-detail strong {
  color: #667eea;
  font-weight: 600;
}

.features-section h2 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.feature-card p {
  color: #666;
  margin: 0;
  line-height: 1.6;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .header {
    padding: 1rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header h1 {
    font-size: 1.6rem;
  }

  .user-info {
    flex-direction: column;
    gap: 0.8rem;
  }

  .main-content {
    padding: 2rem 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .welcome-section h2,
  .features-section h2 {
    font-size: 1.5rem;
  }
}
</style>
