# Vue 用户管理系统

一个基于 Vue 3 + Vite 的前端用户管理系统，包含用户注册、登录和主页功能。

## 功能特性

- 🔐 **用户登录** - 安全的用户登录功能
- 📝 **用户注册** - 简单快捷的注册流程
- 🏠 **个人主页** - 个性化的用户主页
- 💾 **数据持久化** - 使用 localStorage 存储用户数据
- 🛡️ **路由守卫** - 保护需要登录的页面
- 📱 **响应式设计** - 适配移动端和桌面端

## 技术栈

- Vue 3 (Composition API)
- Vue Router 4
- Vite
- 原生 CSS

## 项目结构

```
src/
├── views/           # 页面组件
│   ├── Home.vue     # 主页
│   ├── Login.vue    # 登录页
│   └── Register.vue # 注册页
├── router/          # 路由配置
│   └── index.js     # 路由定义和守卫
├── stores/          # 状态管理
│   └── user.js      # 用户状态管理
├── App.vue          # 根组件
└── main.js          # 应用入口
```

## 快速开始

### 安装依赖

```sh
npm install
```

### 启动开发服务器

```sh
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本

```sh
npm run build
```

## 使用说明

1. **注册新用户**
   - 访问 `/register` 页面
   - 填写用户名、邮箱和密码
   - 点击注册按钮

2. **用户登录**
   - 访问 `/login` 页面
   - 输入注册时的邮箱和密码
   - 点击登录按钮

3. **访问主页**
   - 登录成功后自动跳转到主页
   - 查看个人信息和系统功能
   - 可以点击退出登录

## 数据存储

应用使用浏览器的 localStorage 来存储：
- 用户注册信息 (`users` 键)
- 当前登录用户信息 (`user` 键)

## 路由说明

- `/` - 主页 (需要登录)
- `/login` - 登录页
- `/register` - 注册页

## 开发说明

- 使用 Vue 3 Composition API
- 响应式状态管理使用 `reactive`
- 路由守卫自动处理登录状态检查
- 支持热重载开发
