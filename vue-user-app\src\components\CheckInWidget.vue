<template>
  <div class="checkin-widget">
    <div class="checkin-header">
      <h3>
        <span class="checkin-icon">📅</span>
        每日打卡
      </h3>
      <div class="streak-badge">
        连续{{ consecutiveDays }}天
      </div>
    </div>
    
    <div class="checkin-content">
      <!-- 打卡状态 -->
      <div class="checkin-status">
        <div class="status-circle" :class="{ 'checked': isCheckedToday }">
          <span v-if="isCheckedToday" class="check-mark">✓</span>
          <span v-else class="clock-icon">🕐</span>
        </div>
        <div class="status-text">
          <div class="status-title">
            {{ isCheckedToday ? '今日已打卡' : '今日未打卡' }}
          </div>
          <div class="status-time" v-if="isCheckedToday">
            {{ todayCheckTime }}
          </div>
        </div>
      </div>
      
      <!-- 打卡按钮 -->
      <button 
        @click="handleCheckIn" 
        class="checkin-btn"
        :class="{ 'disabled': isCheckedToday }"
        :disabled="isCheckedToday"
      >
        {{ isCheckedToday ? '已完成打卡' : '立即打卡' }}
      </button>
      
      <!-- 打卡统计 -->
      <div class="checkin-stats">
        <div class="stat-item">
          <div class="stat-number">{{ totalCheckIns }}</div>
          <div class="stat-label">总打卡</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ thisMonthCheckIns }}</div>
          <div class="stat-label">本月</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ consecutiveDays }}</div>
          <div class="stat-label">连续</div>
        </div>
      </div>
      
      <!-- 最近打卡记录 -->
      <div class="recent-checkins">
        <h4>最近打卡</h4>
        <div class="checkin-calendar">
          <div 
            v-for="day in recentDays" 
            :key="day.date"
            class="calendar-day"
            :class="{ 'checked': day.checked, 'today': day.isToday }"
          >
            <div class="day-number">{{ day.dayNumber }}</div>
            <div class="day-status">
              <span v-if="day.checked">✓</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 打卡成功提示 -->
    <div v-if="showSuccessMessage" class="success-message">
      <span class="success-icon">🎉</span>
      {{ successMessage }}
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { userStore } from '../stores/user.js'

export default {
  name: 'CheckInWidget',
  setup() {
    const showSuccessMessage = ref(false)
    const successMessage = ref('')

    const isCheckedToday = computed(() => userStore.getTodayCheckInStatus())
    const consecutiveDays = computed(() => userStore.getConsecutiveCheckInDays())
    
    const totalCheckIns = computed(() => userStore.checkInRecords.length)
    
    const thisMonthCheckIns = computed(() => {
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      return userStore.checkInRecords.filter(record => {
        const recordDate = new Date(record.time)
        return recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear
      }).length
    })
    
    const todayCheckTime = computed(() => {
      const today = new Date().toDateString()
      const todayRecord = userStore.checkInRecords.find(record => record.date === today)
      if (todayRecord) {
        return new Date(todayRecord.time).toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return ''
    })
    
    const recentDays = computed(() => {
      const days = []
      const today = new Date()
      
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)
        const dateStr = date.toDateString()
        
        const isChecked = userStore.checkInRecords.some(record => record.date === dateStr)
        const isToday = i === 0
        
        days.push({
          date: dateStr,
          dayNumber: date.getDate(),
          checked: isChecked,
          isToday
        })
      }
      
      return days
    })

    const handleCheckIn = () => {
      const result = userStore.checkIn()
      
      if (result.success) {
        successMessage.value = result.message
        showSuccessMessage.value = true
        
        setTimeout(() => {
          showSuccessMessage.value = false
        }, 3000)
      } else {
        successMessage.value = result.message
        showSuccessMessage.value = true
        
        setTimeout(() => {
          showSuccessMessage.value = false
        }, 2000)
      }
    }

    return {
      showSuccessMessage,
      successMessage,
      isCheckedToday,
      consecutiveDays,
      totalCheckIns,
      thisMonthCheckIns,
      todayCheckTime,
      recentDays,
      handleCheckIn
    }
  }
}
</script>

<style scoped>
.checkin-widget {
  background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
  color: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 24px rgba(255, 118, 117, 0.3);
  position: relative;
  overflow: hidden;
}

.checkin-widget::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.checkin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.checkin-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.checkin-icon {
  font-size: 1.2rem;
}

.streak-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.checkin-status {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.status-circle {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.status-circle.checked {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.status-text {
  flex: 1;
}

.status-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
}

.status-time {
  font-size: 0.9rem;
  opacity: 0.8;
}

.checkin-btn {
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.checkin-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.checkin-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.checkin-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  text-align: center;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
}

.recent-checkins h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  opacity: 0.9;
}

.checkin-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.calendar-day.checked {
  background: rgba(255, 255, 255, 0.3);
}

.calendar-day.today {
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.day-number {
  margin-bottom: 0.2rem;
}

.day-status {
  font-size: 0.7rem;
  height: 0.8rem;
}

.success-message {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.success-icon {
  font-size: 1.2rem;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .checkin-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .success-message {
    top: 1rem;
    right: 1rem;
    left: 1rem;
  }
}
</style>
