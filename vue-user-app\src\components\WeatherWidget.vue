<template>
  <div class="weather-widget">
    <div class="weather-header">
      <h3>
        <span class="weather-icon">🌤️</span>
        天气预报
      </h3>
      <button @click="refreshWeather" class="refresh-btn" :disabled="loading">
        <span class="refresh-icon" :class="{ 'spinning': loading }">🔄</span>
      </button>
    </div>
    
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>获取天气信息中...</p>
    </div>
    
    <div v-else class="weather-content">
      <!-- 当前天气 -->
      <div class="current-weather">
        <div class="weather-main">
          <div class="temperature">{{ currentWeather.temperature }}°C</div>
          <div class="weather-desc">{{ currentWeather.description }}</div>
        </div>
        <div class="weather-emoji">{{ currentWeather.emoji }}</div>
      </div>
      
      <!-- 详细信息 -->
      <div class="weather-details">
        <div class="detail-item">
          <span class="detail-icon">💧</span>
          <span class="detail-label">湿度</span>
          <span class="detail-value">{{ currentWeather.humidity }}%</span>
        </div>
        <div class="detail-item">
          <span class="detail-icon">💨</span>
          <span class="detail-label">风速</span>
          <span class="detail-value">{{ currentWeather.windSpeed }} km/h</span>
        </div>
        <div class="detail-item">
          <span class="detail-icon">👁️</span>
          <span class="detail-label">能见度</span>
          <span class="detail-value">{{ currentWeather.visibility }} km</span>
        </div>
      </div>
      
      <!-- 未来几天预报 -->
      <div class="forecast">
        <h4>未来3天</h4>
        <div class="forecast-list">
          <div 
            v-for="day in forecast" 
            :key="day.date" 
            class="forecast-item"
          >
            <div class="forecast-date">{{ day.dayName }}</div>
            <div class="forecast-emoji">{{ day.emoji }}</div>
            <div class="forecast-temp">
              <span class="temp-high">{{ day.high }}°</span>
              <span class="temp-low">{{ day.low }}°</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="weather-footer">
        <span class="update-time">更新时间: {{ updateTime }}</span>
        <span class="location">📍 北京</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'WeatherWidget',
  setup() {
    const loading = ref(false)
    const currentWeather = ref({})
    const forecast = ref([])
    const updateTime = ref('')

    // 模拟天气数据
    const weatherConditions = [
      { emoji: '☀️', description: '晴朗', temp: [18, 25] },
      { emoji: '⛅', description: '多云', temp: [15, 22] },
      { emoji: '🌧️', description: '小雨', temp: [12, 18] },
      { emoji: '🌦️', description: '阵雨', temp: [14, 20] },
      { emoji: '🌤️', description: '晴转多云', temp: [16, 24] },
      { emoji: '🌥️', description: '阴天', temp: [13, 19] }
    ]

    const getRandomWeather = () => {
      return weatherConditions[Math.floor(Math.random() * weatherConditions.length)]
    }

    const generateWeatherData = () => {
      const current = getRandomWeather()
      const currentTemp = Math.floor(Math.random() * (current.temp[1] - current.temp[0] + 1)) + current.temp[0]
      
      currentWeather.value = {
        temperature: currentTemp,
        description: current.description,
        emoji: current.emoji,
        humidity: Math.floor(Math.random() * 40) + 40, // 40-80%
        windSpeed: Math.floor(Math.random() * 15) + 5, // 5-20 km/h
        visibility: Math.floor(Math.random() * 10) + 10 // 10-20 km
      }

      // 生成未来3天预报
      const days = ['明天', '后天', '大后天']
      forecast.value = days.map((dayName, index) => {
        const weather = getRandomWeather()
        return {
          date: new Date(Date.now() + (index + 1) * 24 * 60 * 60 * 1000).toDateString(),
          dayName,
          emoji: weather.emoji,
          high: Math.floor(Math.random() * (weather.temp[1] - weather.temp[0] + 1)) + weather.temp[0],
          low: Math.floor(Math.random() * (weather.temp[1] - weather.temp[0] + 1)) + weather.temp[0] - 5
        }
      })

      updateTime.value = new Date().toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const refreshWeather = async () => {
      loading.value = true
      
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      generateWeatherData()
      loading.value = false
    }

    onMounted(() => {
      generateWeatherData()
    })

    return {
      loading,
      currentWeather,
      forecast,
      updateTime,
      refreshWeather
    }
  }
}
</script>

<style scoped>
.weather-widget {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 24px rgba(116, 185, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.weather-widget::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.weather-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.weather-icon {
  font-size: 1.2rem;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-icon {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-state {
  text-align: center;
  padding: 2rem 0;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.current-weather {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.temperature {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.weather-desc {
  font-size: 1rem;
  opacity: 0.9;
}

.weather-emoji {
  font-size: 3rem;
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.detail-icon {
  font-size: 1.2rem;
  margin-bottom: 0.3rem;
}

.detail-label {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-bottom: 0.2rem;
}

.detail-value {
  font-size: 0.9rem;
  font-weight: 600;
}

.forecast h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  opacity: 0.9;
}

.forecast-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.8rem;
}

.forecast-item {
  text-align: center;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.forecast-date {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-bottom: 0.5rem;
}

.forecast-emoji {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.forecast-temp {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.temp-high {
  font-weight: 600;
}

.temp-low {
  opacity: 0.7;
}

.weather-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.8rem;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .weather-details {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .forecast-list {
    grid-template-columns: 1fr;
  }
  
  .weather-footer {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
</style>
