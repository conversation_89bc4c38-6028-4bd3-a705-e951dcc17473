<template>
  <div class="task-manager">
    <!-- 待办任务 -->
    <div class="task-section">
      <div class="task-header" @click="showTodoDetails = !showTodoDetails">
        <h3>
          <span class="task-icon">📋</span>
          待办任务
          <span class="task-count">{{ todoTasks.length }}</span>
        </h3>
        <span class="toggle-icon" :class="{ 'rotated': showTodoDetails }">▼</span>
      </div>
      
      <div v-if="showTodoDetails" class="task-details">
        <div v-if="todoTasks.length === 0" class="empty-state">
          暂无待办任务
        </div>
        <div v-else class="task-list">
          <div 
            v-for="task in todoTasks" 
            :key="task.id" 
            class="task-item"
            :class="'priority-' + task.priority"
          >
            <div class="task-content">
              <h4>{{ task.title }}</h4>
              <p>{{ task.description }}</p>
              <div class="task-meta">
                <span class="priority-badge" :class="'priority-' + task.priority">
                  {{ getPriorityText(task.priority) }}
                </span>
                <span class="task-date">{{ formatDate(task.createdAt) }}</span>
              </div>
            </div>
            <button 
              @click="handleTask(task.id)" 
              class="process-btn"
            >
              处理
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 已办任务 -->
    <div class="task-section">
      <div class="task-header" @click="showCompletedDetails = !showCompletedDetails">
        <h3>
          <span class="task-icon">✅</span>
          已办任务
          <span class="task-count">{{ completedTasks.length }}</span>
        </h3>
        <span class="toggle-icon" :class="{ 'rotated': showCompletedDetails }">▼</span>
      </div>
      
      <div v-if="showCompletedDetails" class="task-details">
        <div v-if="completedTasks.length === 0" class="empty-state">
          暂无已办任务
        </div>
        <div v-else class="task-list">
          <div 
            v-for="task in completedTasks" 
            :key="task.id" 
            class="task-item completed"
          >
            <div class="task-content">
              <h4>{{ task.title }}</h4>
              <p>{{ task.description }}</p>
              <div class="task-meta">
                <span class="completed-badge">已完成</span>
                <span class="task-date">{{ formatDate(task.completedAt) }}</span>
              </div>
            </div>
            <div class="completed-icon">✓</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理成功提示 -->
    <div v-if="showSuccessMessage" class="success-message">
      <span class="success-icon">🎉</span>
      处理成功！任务已移至已办列表
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { userStore } from '../stores/user.js'

export default {
  name: 'TaskManager',
  setup() {
    const showTodoDetails = ref(false)
    const showCompletedDetails = ref(false)
    const showSuccessMessage = ref(false)

    const todoTasks = computed(() => userStore.todoTasks)
    const completedTasks = computed(() => userStore.completedTasks)

    const handleTask = (taskId) => {
      const success = userStore.completeTask(taskId)
      if (success) {
        showSuccessMessage.value = true
        setTimeout(() => {
          showSuccessMessage.value = false
        }, 3000)
      }
    }

    const getPriorityText = (priority) => {
      const priorityMap = {
        high: '高优先级',
        medium: '中优先级',
        low: '低优先级'
      }
      return priorityMap[priority] || '普通'
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    return {
      showTodoDetails,
      showCompletedDetails,
      showSuccessMessage,
      todoTasks,
      completedTasks,
      handleTask,
      getPriorityText,
      formatDate
    }
  }
}
</script>

<style scoped>
.task-manager {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.task-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.task-header {
  padding: 1.2rem 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background 0.3s ease;
}

.task-header:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.task-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  font-size: 1.1rem;
}

.task-icon {
  font-size: 1.2rem;
}

.task-count {
  background: #667eea;
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.toggle-icon {
  transition: transform 0.3s ease;
  color: #666;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

.task-details {
  padding: 1rem 1.5rem 1.5rem;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 2rem;
  font-style: italic;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.task-item:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.1);
}

.task-item.completed {
  opacity: 0.8;
  border-color: #28a745;
}

.task-item.priority-high {
  border-left: 4px solid #dc3545;
}

.task-item.priority-medium {
  border-left: 4px solid #ffc107;
}

.task-item.priority-low {
  border-left: 4px solid #28a745;
}

.task-content {
  flex: 1;
}

.task-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.task-content p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.priority-badge {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.priority-high {
  background: #fee;
  color: #dc3545;
}

.priority-badge.priority-medium {
  background: #fff8e1;
  color: #f57c00;
}

.priority-badge.priority-low {
  background: #e8f5e8;
  color: #28a745;
}

.completed-badge {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  background: #e8f5e8;
  color: #28a745;
}

.task-date {
  font-size: 0.8rem;
  color: #999;
}

.process-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.process-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.completed-icon {
  width: 2rem;
  height: 2rem;
  background: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.success-message {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.success-icon {
  font-size: 1.2rem;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .process-btn {
    align-self: stretch;
    text-align: center;
  }
  
  .success-message {
    top: 1rem;
    right: 1rem;
    left: 1rem;
  }
}
</style>
