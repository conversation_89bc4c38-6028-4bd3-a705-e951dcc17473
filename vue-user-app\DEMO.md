# Vue 用户管理系统演示

## 项目概述

这是一个功能丰富的现代化用户管理系统，基于 Vue 3 构建，包含完整的用户认证、任务管理、数据统计、天气预报和打卡功能。

## 🚀 核心功能

### 1. 用户认证系统
- **用户注册**: 访问 `/register` 创建新账户
- **用户登录**: 访问 `/login` 使用邮箱密码登录
- **自动路由守卫**: 未登录用户自动跳转到登录页

### 2. 智能主页仪表板
登录成功后，主页包含以下功能模块：

#### 📊 会话统计信息
- **今日会话数**: 显示当天登录次数
- **7天趋势图**: 可视化展示最近一周的会话活动
- **活跃时段分析**: 显示不同时间段的使用频率
- **统计摘要**: 平均每日、最高单日、活跃天数等数据

#### 📋 任务管理系统
- **待办任务列表**: 显示待处理任务数量和详情
- **已办任务列表**: 显示已完成任务的历史记录
- **任务处理**: 点击"处理"按钮将待办任务移至已办
- **优先级标识**: 高/中/低优先级的颜色区分
- **任务详情**: 包含标题、描述、创建时间等信息

#### 🌤️ 天气预报
- **实时天气**: 显示当前温度、天气状况
- **详细信息**: 湿度、风速、能见度等气象数据
- **3天预报**: 未来三天的天气趋势
- **自动刷新**: 支持手动刷新天气数据

#### 📅 每日打卡功能
- **打卡状态**: 显示今日是否已打卡
- **连续打卡**: 统计连续打卡天数
- **打卡历史**: 最近7天的打卡记录可视化
- **打卡统计**: 总打卡数、本月打卡数等数据

#### 👤 用户信息展示
- **个性化头像**: 基于用户名自动生成的彩色头像
- **智能问候**: 根据时间显示不同的问候语
- **个人资料**: 用户名、邮箱、注册时间等信息

## 设计特色

### 🎨 现代化UI设计
- **渐变背景**: 使用紫色渐变背景，营造现代感
- **毛玻璃效果**: 表单采用毛玻璃效果，增加视觉层次
- **圆角设计**: 16px圆角，符合现代设计趋势
- **阴影效果**: 多层阴影，增强立体感

### 🎯 交互体验
- **悬停效果**: 按钮和卡片的悬停动画
- **焦点状态**: 输入框聚焦时的边框和阴影变化
- **响应式设计**: 适配移动端和桌面端
- **平滑过渡**: 所有交互都有平滑的过渡动画

### 🔧 技术特性
- **Vue 3 Composition API**: 使用最新的Vue语法
- **Vue Router**: 单页应用路由管理
- **路由守卫**: 自动处理登录状态检查
- **本地存储**: 使用localStorage持久化数据
- **响应式状态管理**: 使用reactive进行状态管理

## 测试账户

由于使用本地存储，您需要先注册一个账户才能登录。

建议测试流程：
1. 访问注册页面创建账户
2. 使用创建的账户登录
3. 体验主页功能

## 技术栈

- **前端框架**: Vue 3
- **构建工具**: Vite
- **路由管理**: Vue Router 4
- **样式**: 原生CSS + 现代设计
- **状态管理**: Vue 3 Reactive API

## 启动项目

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问应用
http://localhost:5173
```

## 项目结构

```
src/
├── views/           # 页面组件
│   ├── Home.vue     # 主页 - 渐变背景 + 用户信息展示
│   ├── Login.vue    # 登录页 - 紫色渐变 + 毛玻璃表单
│   └── Register.vue # 注册页 - 绿色渐变 + 毛玻璃表单
├── router/          # 路由配置
├── stores/          # 状态管理
└── assets/          # 静态资源和全局样式
```

## 设计亮点

1. **一致的视觉语言**: 所有页面都采用渐变背景和毛玻璃效果
2. **品牌色彩**: 主要使用紫色系渐变，注册页面使用绿色系
3. **微交互**: 按钮悬停、输入框聚焦等细节动画
4. **无障碍设计**: 良好的对比度和焦点指示器
5. **移动优先**: 响应式设计，移动端体验优秀
