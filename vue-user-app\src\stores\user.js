import { reactive } from 'vue'

export const userStore = reactive({
  user: null,
  isAuthenticated: false,

  // 登录
  login(userData) {
    this.user = userData
    this.isAuthenticated = true
    localStorage.setItem('user', JSON.stringify(userData))
  },

  // 登出
  logout() {
    this.user = null
    this.isAuthenticated = false
    localStorage.removeItem('user')
  },

  // 初始化用户状态
  initUser() {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      this.user = JSON.parse(savedUser)
      this.isAuthenticated = true
    }
  },

  // 注册用户
  register(userData) {
    // 模拟注册逻辑
    const users = JSON.parse(localStorage.getItem('users') || '[]')
    
    // 检查用户是否已存在
    const existingUser = users.find(user => user.email === userData.email)
    if (existingUser) {
      throw new Error('用户已存在')
    }

    // 添加新用户
    const newUser = {
      id: Date.now(),
      ...userData,
      createdAt: new Date().toISOString()
    }
    users.push(newUser)
    localStorage.setItem('users', JSON.stringify(users))
    
    return newUser
  },

  // 验证登录
  authenticate(email, password) {
    const users = JSON.parse(localStorage.getItem('users') || '[]')
    const user = users.find(user => user.email === email && user.password === password)
    
    if (!user) {
      throw new Error('邮箱或密码错误')
    }
    
    return user
  }
})
