import { reactive } from 'vue'

export const userStore = reactive({
  user: null,
  isAuthenticated: false,
  sessions: [],
  todoTasks: [],
  completedTasks: [],
  checkInRecords: [],

  // 登录
  login(userData) {
    this.user = userData
    this.isAuthenticated = true
    localStorage.setItem('user', JSON.stringify(userData))

    // 记录登录会话
    this.addSession()

    // 初始化用户数据
    this.initUserData()
  },

  // 登出
  logout() {
    this.user = null
    this.isAuthenticated = false
    localStorage.removeItem('user')
  },

  // 初始化用户状态
  initUser() {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      this.user = JSON.parse(savedUser)
      this.isAuthenticated = true
      this.initUserData()
    }
  },

  // 初始化用户数据
  initUserData() {
    if (!this.user) return

    const userId = this.user.id
    this.sessions = JSON.parse(localStorage.getItem(`sessions_${userId}`) || '[]')
    this.todoTasks = JSON.parse(localStorage.getItem(`todoTasks_${userId}`) || '[]')
    this.completedTasks = JSON.parse(localStorage.getItem(`completedTasks_${userId}`) || '[]')
    this.checkInRecords = JSON.parse(localStorage.getItem(`checkInRecords_${userId}`) || '[]')

    // 如果是新用户，初始化一些示例数据
    if (this.todoTasks.length === 0) {
      this.initSampleData()
    }
  },

  // 注册用户
  register(userData) {
    // 模拟注册逻辑
    const users = JSON.parse(localStorage.getItem('users') || '[]')
    
    // 检查用户是否已存在
    const existingUser = users.find(user => user.email === userData.email)
    if (existingUser) {
      throw new Error('用户已存在')
    }

    // 添加新用户
    const newUser = {
      id: Date.now(),
      ...userData,
      createdAt: new Date().toISOString()
    }
    users.push(newUser)
    localStorage.setItem('users', JSON.stringify(users))
    
    return newUser
  },

  // 验证登录
  authenticate(email, password) {
    const users = JSON.parse(localStorage.getItem('users') || '[]')
    const user = users.find(user => user.email === email && user.password === password)

    if (!user) {
      throw new Error('邮箱或密码错误')
    }

    return user
  },

  // 初始化示例数据
  initSampleData() {
    const sampleTodos = [
      { id: 1, title: '完成项目报告', description: '撰写Q4项目总结报告', priority: 'high', createdAt: new Date().toISOString() },
      { id: 2, title: '团队会议准备', description: '准备下周团队会议的议程和材料', priority: 'medium', createdAt: new Date().toISOString() },
      { id: 3, title: '代码审查', description: '审查新功能的代码实现', priority: 'high', createdAt: new Date().toISOString() },
      { id: 4, title: '客户反馈整理', description: '整理本月客户反馈并制定改进计划', priority: 'low', createdAt: new Date().toISOString() }
    ]

    const sampleCompleted = [
      { id: 101, title: '系统部署', description: '完成生产环境系统部署', priority: 'high', completedAt: new Date(Date.now() - 86400000).toISOString() },
      { id: 102, title: '文档更新', description: '更新API文档', priority: 'medium', completedAt: new Date(Date.now() - 172800000).toISOString() }
    ]

    this.todoTasks = sampleTodos
    this.completedTasks = sampleCompleted
    this.saveUserData()
  },

  // 保存用户数据
  saveUserData() {
    if (!this.user) return

    const userId = this.user.id
    localStorage.setItem(`sessions_${userId}`, JSON.stringify(this.sessions))
    localStorage.setItem(`todoTasks_${userId}`, JSON.stringify(this.todoTasks))
    localStorage.setItem(`completedTasks_${userId}`, JSON.stringify(this.completedTasks))
    localStorage.setItem(`checkInRecords_${userId}`, JSON.stringify(this.checkInRecords))
  },

  // 添加会话记录
  addSession() {
    const today = new Date().toDateString()
    const existingSession = this.sessions.find(s => s.date === today)

    if (existingSession) {
      existingSession.count++
      existingSession.lastLogin = new Date().toISOString()
    } else {
      this.sessions.push({
        date: today,
        count: 1,
        lastLogin: new Date().toISOString()
      })
    }

    this.saveUserData()
  },

  // 获取今日会话数
  getTodaySessions() {
    const today = new Date().toDateString()
    const todaySession = this.sessions.find(s => s.date === today)
    return todaySession ? todaySession.count : 0
  },

  // 获取最近7天会话统计
  getWeekSessions() {
    const last7Days = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toDateString()
      const session = this.sessions.find(s => s.date === dateStr)
      last7Days.push({
        date: dateStr,
        count: session ? session.count : 0
      })
    }
    return last7Days
  },

  // 处理待办任务
  completeTask(taskId) {
    const taskIndex = this.todoTasks.findIndex(task => task.id === taskId)
    if (taskIndex !== -1) {
      const task = this.todoTasks.splice(taskIndex, 1)[0]
      task.completedAt = new Date().toISOString()
      this.completedTasks.unshift(task)
      this.saveUserData()
      return true
    }
    return false
  },

  // 打卡
  checkIn() {
    const today = new Date().toDateString()
    const existingCheckIn = this.checkInRecords.find(record => record.date === today)

    if (existingCheckIn) {
      return { success: false, message: '今日已打卡' }
    }

    this.checkInRecords.push({
      date: today,
      time: new Date().toISOString(),
      status: 'checked'
    })

    this.saveUserData()
    return { success: true, message: '打卡成功！' }
  },

  // 获取打卡状态
  getTodayCheckInStatus() {
    const today = new Date().toDateString()
    return this.checkInRecords.some(record => record.date === today)
  },

  // 获取连续打卡天数
  getConsecutiveCheckInDays() {
    if (this.checkInRecords.length === 0) return 0

    let consecutive = 0
    const today = new Date()

    for (let i = 0; i < 30; i++) {
      const checkDate = new Date(today)
      checkDate.setDate(today.getDate() - i)
      const dateStr = checkDate.toDateString()

      if (this.checkInRecords.some(record => record.date === dateStr)) {
        consecutive++
      } else {
        break
      }
    }

    return consecutive
  }
})
