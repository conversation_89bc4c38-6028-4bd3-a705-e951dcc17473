{"name": "error-stack-parser-es", "type": "module", "version": "0.1.5", "packageManager": "pnpm@9.5.0", "description": "Cross-browser Error parser", "maintainers": ["<PERSON> <<EMAIL>> (https://antfu.me)", "<PERSON> <<EMAIL>> (https://www.eriwen.com)", "<PERSON> <vkhomy<PERSON><PERSON>@gmail.com> (https://github.com/victor-ho<PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/oliversalzburg)", "<PERSON> (https://github.com/bengourley)"], "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/error-stack-parser-es#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/error-stack-parser-es.git"}, "bugs": "https://github.com/antfu/error-stack-parser-es/issues", "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./lite": {"types": "./dist/lite.d.ts", "import": "./dist/lite.mjs", "require": "./dist/lite.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp && npm publish", "start": "esno src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit"}, "devDependencies": {"@antfu/eslint-config": "^2.22.4", "@antfu/ni": "^0.22.0", "@antfu/utils": "^0.7.10", "@types/node": "^20.14.11", "bumpp": "^9.4.1", "eslint": "^9.7.0", "esno": "^4.7.0", "lint-staged": "^15.2.7", "pnpm": "^9.5.0", "rimraf": "^6.0.1", "simple-git-hooks": "^2.11.1", "typescript": "^5.5.3", "unbuild": "^2.0.0", "vite": "^5.3.4", "vitest": "^2.0.3"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}